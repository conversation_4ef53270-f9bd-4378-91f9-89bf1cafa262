#!/bin/bash

# SimWeaver Control Script
# Quick commands for controlling SimWeaver crawler simulator

BASE_URL="http://localhost:8000"
API_URL="$BASE_URL/api/crawl"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper function to print colored output
print_status() {
    echo -e "${GREEN}[SimWeaver]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[Warning]${NC} $1"
}

print_error() {
    echo -e "${RED}[Error]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[Info]${NC} $1"
}

# Check if server is running
check_server() {
    if curl -s "$BASE_URL/api/health" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Main command handler
case "$1" in
    "start")
        print_status "Starting SimWeaver server..."
        uv run python -m spigaui.main
        ;;
    
    "status")
        if check_server; then
            print_status "Server is running. Getting engine status..."
            curl -s "$API_URL/status/engine" | jq '.' 2>/dev/null || curl -s "$API_URL/status/engine"
        else
            print_error "Server is not running. Use './simweaver.sh start' to start it."
        fi
        ;;
    
    "demo")
        if check_server; then
            print_status "Creating demo crawl jobs..."
            curl -s -X POST "$API_URL/demo" | jq '.' 2>/dev/null || curl -s -X POST "$API_URL/demo"
        else
            print_error "Server is not running. Use './simweaver.sh start' to start it."
        fi
        ;;
    
    "jobs")
        if check_server; then
            print_status "Listing all jobs..."
            curl -s "$API_URL" | jq '.' 2>/dev/null || curl -s "$API_URL"
        else
            print_error "Server is not running. Use './simweaver.sh start' to start it."
        fi
        ;;
    
    "stop-all")
        if check_server; then
            print_warning "Emergency stopping all jobs..."
            curl -s -X POST "$API_URL/emergency/stop-all" | jq '.' 2>/dev/null || curl -s -X POST "$API_URL/emergency/stop-all"
        else
            print_error "Server is not running."
        fi
        ;;
    
    "kill-engine")
        if check_server; then
            print_error "NUCLEAR OPTION: Resetting entire engine (destroys all data)..."
            read -p "Are you sure? Type 'yes' to confirm: " confirm
            if [ "$confirm" = "yes" ]; then
                curl -s -X POST "$API_URL/emergency/kill-engine" | jq '.' 2>/dev/null || curl -s -X POST "$API_URL/emergency/kill-engine"
            else
                print_info "Operation cancelled."
            fi
        else
            print_error "Server is not running."
        fi
        ;;
    
    "logs")
        print_status "Watching SimWeaver logs (Ctrl+C to stop)..."
        tail -f logs/spigaui.log
        ;;
    
    "logs-errors")
        print_status "Watching error logs only (Ctrl+C to stop)..."
        tail -f logs/spigaui.log | grep '"level": "error"'
        ;;
    
    "logs-metrics")
        print_status "Watching metrics logs only (Ctrl+C to stop)..."
        tail -f logs/spigaui.log | grep '"category": "metrics"'
        ;;
    
    "preset")
        if [ -z "$2" ]; then
            print_error "Usage: ./simweaver.sh preset <preset_name>"
            print_info "Available presets: minimal, debug, performance, error_testing, network, full_trace, production"
            exit 1
        fi
        
        if check_server; then
            print_status "Setting logging preset to: $2"
            curl -s -X POST "$API_URL/logging/preset/$2" | jq '.' 2>/dev/null || curl -s -X POST "$API_URL/logging/preset/$2"
        else
            print_error "Server is not running. Use './simweaver.sh start' to start it."
        fi
        ;;
    
    "test-logs")
        if check_server; then
            print_status "Testing all logging categories..."
            curl -s -X POST "$API_URL/logging/test" | jq '.' 2>/dev/null || curl -s -X POST "$API_URL/logging/test"
        else
            print_error "Server is not running. Use './simweaver.sh start' to start it."
        fi
        ;;
    
    "help"|"--help"|"-h"|"")
        echo -e "${GREEN}SimWeaver Control Script${NC}"
        echo ""
        echo "Usage: ./simweaver.sh <command> [options]"
        echo ""
        echo "Commands:"
        echo "  start              Start SimWeaver server"
        echo "  status             Check server and engine status"
        echo "  demo               Create demo crawl jobs"
        echo "  jobs               List all jobs"
        echo "  stop-all           Emergency stop all jobs (safe)"
        echo "  kill-engine        Nuclear reset - destroys all data (destructive)"
        echo "  logs               Watch all logs in real-time"
        echo "  logs-errors        Watch error logs only"
        echo "  logs-metrics       Watch metrics logs only"
        echo "  preset <name>      Set logging preset (minimal, debug, performance, etc.)"
        echo "  test-logs          Test all logging categories"
        echo "  help               Show this help message"
        echo ""
        echo "Examples:"
        echo "  ./simweaver.sh start"
        echo "  ./simweaver.sh demo"
        echo "  ./simweaver.sh preset debug"
        echo "  ./simweaver.sh logs-errors"
        echo "  ./simweaver.sh stop-all"
        echo ""
        echo "Documentation:"
        echo "  API Docs:     http://localhost:8000/docs"
        echo "  Commands:     docs/simweaver_commands.md"
        echo "  Full Guide:   docs/sim_weaver.md"
        ;;
    
    *)
        print_error "Unknown command: $1"
        print_info "Use './simweaver.sh help' for available commands"
        exit 1
        ;;
esac
