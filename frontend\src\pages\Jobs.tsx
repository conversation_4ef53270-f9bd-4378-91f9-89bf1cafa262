import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { crawlApi } from '../lib/api'

const Jobs: React.FC = () => {
  const { data: jobs, isLoading, error } = useQuery({
    queryKey: ['jobs'],
    queryFn: crawlApi.getJobs,
    refetchInterval: 2000, // Refresh every 2 seconds for real-time updates
  })

  const handleJobAction = async (jobId: string, action: 'start' | 'stop' | 'pause' | 'resume') => {
    try {
      let result
      switch (action) {
        case 'start':
          result = await crawlApi.startJob(jobId)
          break
        case 'stop':
          result = await crawlApi.stopJob(jobId)
          break
        case 'pause':
          result = await crawlApi.pauseJob(jobId)
          break
        case 'resume':
          result = await crawlApi.resumeJob(jobId)
          break
      }
      alert(result.message)
    } catch (error) {
      alert(`Failed to ${action} job`)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-500'
      case 'completed': return 'bg-blue-500'
      case 'failed': return 'bg-red-500'
      case 'cancelled': return 'bg-gray-500'
      case 'starting': return 'bg-yellow-500'
      case 'queued': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  const getActionButtons = (job: any) => {
    switch (job.status) {
      case 'queued':
        return (
          <button
            onClick={() => handleJobAction(job.job_id, 'start')}
            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
          >
            Start
          </button>
        )
      case 'running':
        return (
          <div className="flex space-x-2">
            <button
              onClick={() => handleJobAction(job.job_id, 'pause')}
              className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm"
            >
              Pause
            </button>
            <button
              onClick={() => handleJobAction(job.job_id, 'stop')}
              className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
            >
              Stop
            </button>
          </div>
        )
      case 'paused':
        return (
          <div className="flex space-x-2">
            <button
              onClick={() => handleJobAction(job.job_id, 'resume')}
              className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
            >
              Resume
            </button>
            <button
              onClick={() => handleJobAction(job.job_id, 'stop')}
              className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
            >
              Stop
            </button>
          </div>
        )
      default:
        return (
          <span className="text-gray-400 text-sm">No actions</span>
        )
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400">Failed to load jobs</p>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Jobs</h1>
        <p className="text-gray-400">Manage and monitor crawl jobs</p>
      </div>

      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-700">
          <h2 className="text-lg font-medium text-white">All Jobs ({jobs?.length || 0})</h2>
        </div>
        
        {jobs && jobs.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-700">
              <thead className="bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    URL
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Performance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-gray-800 divide-y divide-gray-700">
                {jobs.map((job) => (
                  <tr key={job.job_id} className="hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full mr-2 ${getStatusColor(job.status)}`}></div>
                        <span className="text-white capitalize">{job.status}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-white font-medium truncate max-w-xs">{job.start_url}</div>
                      <div className="text-gray-400 text-sm">ID: {job.job_id.slice(0, 8)}...</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-white">
                        {job.stats.pages_crawled}/{job.stats.total_pages} pages
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2 mt-1">
                        <div
                          className="bg-blue-500 h-2 rounded-full"
                          style={{
                            width: `${job.stats.total_pages > 0 ? (job.stats.pages_crawled / job.stats.total_pages) * 100 : 0}%`
                          }}
                        ></div>
                      </div>
                      <div className="text-gray-400 text-sm mt-1">
                        {job.stats.total_pages > 0 ? (((job.stats.pages_crawled - job.stats.pages_failed) / job.stats.total_pages) * 100).toFixed(1) : 0}% success
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-white">{job.stats.pages_per_second.toFixed(2)} p/s</div>
                      {job.stats.pages_failed > 0 && (
                        <div className="text-red-400 text-sm">{job.stats.pages_failed} failed</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-gray-400 text-sm">
                      {new Date(job.created_at).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getActionButtons(job)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="px-6 py-8 text-center text-gray-400">
            <p className="mb-4">No jobs found</p>
            <p className="text-sm">Create some demo jobs from the Dashboard to get started!</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default Jobs
