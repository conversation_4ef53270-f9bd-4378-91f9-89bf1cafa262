import React, { useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { crawlApi } from '../lib/api'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation()

  // Get engine status for header
  const { data: engineStatus } = useQuery({
    queryKey: ['engine-status'],
    queryFn: crawlApi.getEngineStatus,
    refetchInterval: 5000,
  })

  const isActivePage = (path: string) => {
    return location.pathname === path
  }

  const navigation = [
    { name: 'Dashboard', href: '/', icon: '📊' },
    { name: 'Jobs', href: '/jobs', icon: '⚙️' },
    { name: 'Logs', href: '/logs', icon: '📋' },
  ]

  // Update page title based on current route
  useEffect(() => {
    const currentPage = navigation.find(item => item.href === location.pathname)
    const pageTitle = currentPage ? `SpigaUI - ${currentPage.name}` : 'SpigaUI'
    document.title = pageTitle
  }, [location.pathname, navigation])

  return (
    <div className="flex min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <aside className="hidden md:block w-64 bg-white dark:bg-gray-800 shadow-sm border-r border-gray-200 dark:border-gray-700 fixed h-full top-0 left-0 z-10 pt-16">
        <div className="p-4">
          <nav className="space-y-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`flex items-center px-4 py-3 text-base font-medium rounded-lg transition-colors ${
                  isActivePage(item.href)
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                    : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                }`}
              >
                <span className="mr-3">{item.icon}</span>
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      </aside>

      {/* Main content */}
      <div className="w-full md:ml-64">
        {/* Top Navigation */}
        <nav className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <Link to="/" className="flex items-center">
                  <span className="text-xl font-semibold text-gray-900 dark:text-white">
                    SpigaUI
                  </span>
                  <span className="ml-3 text-sm text-gray-500 dark:text-gray-400 hidden md:block">
                    Web Crawler Management
                  </span>
                </Link>
              </div>
              
              <div className="flex items-center space-x-3">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  engineStatus?.engine_healthy
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {engineStatus?.engine_healthy ? 'Healthy' : 'Unhealthy'}
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {engineStatus?.active_jobs || 0} active jobs
                </span>
              </div>
            </div>
          </div>
        </nav>

        {/* Mobile Navigation */}
        <div className="md:hidden border-b border-gray-200 dark:border-gray-700">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`flex items-center block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  isActivePage(item.href)
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white'
                }`}
              >
                <span className="mr-2">{item.icon}</span>
                {item.name}
              </Link>
            ))}
          </div>
        </div>

        {/* Page Content */}
        <main className="p-4">
          {children}
        </main>
      </div>
    </div>
  )
}

export default Layout
