import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { Button } from 'flowbite-react'
import { crawlApi } from '../lib/api'
import { useToast } from '../hooks/useToast'

const System: React.FC = () => {
  const { showSuccess, showError } = useToast()

  const { data: engineStatus, isLoading } = useQuery({
    queryKey: ['engine-status'],
    queryFn: crawlApi.getEngineStatus,
    refetchInterval: 5000,
  })

  const handleEmergencyStop = async () => {
    if (confirm('Are you sure you want to stop all jobs?')) {
      try {
        await crawlApi.emergencyStopAll()
        showSuccess('All jobs stopped!')
      } catch (error) {
        showError('Failed to stop jobs')
      }
    }
  }

  const handleKillEngine = async () => {
    if (confirm('⚠️ NUCLEAR OPTION: This will destroy ALL job data and reset the engine completely. Are you absolutely sure?')) {
      if (confirm('This action cannot be undone. Type YES to confirm you want to proceed with the nuclear reset.')) {
        try {
          await crawlApi.killEngine()
          showSuccess('🔥 Nuclear reset completed! Engine has been completely reset.')
        } catch (error) {
          showError('Failed to reset engine')
        }
      }
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">System Controls</h1>
        <p className="text-gray-400">Emergency controls and system management for SimWeaver</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Emergency Controls */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-medium text-white mb-3">🚨 Emergency Controls</h3>
          <p className="text-gray-400 mb-4">Use these controls when the system needs immediate intervention</p>
          
          <div className="space-y-4">
            {/* Level 1: Safe Stop */}
            <div className="border border-yellow-600 rounded-lg p-4 bg-yellow-900/20">
              <div className="flex items-center mb-2">
                <span className="text-yellow-400 mr-2">🛑</span>
                <h4 className="text-white font-medium">Level 1: Emergency Stop (Safe)</h4>
              </div>
              <p className="text-gray-300 text-sm mb-3">
                Gracefully stops all running jobs while preserving job data and state.
              </p>
              <Button
                onClick={handleEmergencyStop}
                color="yellow"
                className="w-full"
              >
                <span className="mr-2">🛑</span>
                Emergency Stop All Jobs
              </Button>
            </div>

            {/* Level 2: Nuclear Reset */}
            <div className="border border-red-600 rounded-lg p-4 bg-red-900/20">
              <div className="flex items-center mb-2">
                <span className="text-red-400 mr-2">💥</span>
                <h4 className="text-white font-medium">Level 2: Nuclear Reset (Destructive)</h4>
              </div>
              <p className="text-gray-300 text-sm mb-3">
                <strong className="text-red-400">⚠️ DESTROYS ALL DATA:</strong> Complete engine reset. 
                Use only if emergency stop fails or engine is corrupted.
              </p>
              <Button
                onClick={handleKillEngine}
                color="red"
                className="w-full !bg-red-900 !text-white hover:!bg-red-800 hover:!text-white"
              >
                <span className="mr-2">💥</span>
                Nuclear Reset Engine
              </Button>
            </div>
          </div>
        </div>

        {/* System Information */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-medium text-white mb-3">📊 System Information</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Backend Status:</span>
              <span className="px-2 py-1 rounded text-sm font-medium bg-green-900 text-green-200">
                Connected
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Engine Health:</span>
              <span className={`px-2 py-1 rounded text-sm font-medium ${
                engineStatus?.engine_healthy
                  ? 'bg-green-900 text-green-200'
                  : 'bg-red-900 text-red-200'
              }`}>
                {engineStatus?.engine_healthy ? 'Healthy' : 'Unhealthy'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Active Jobs:</span>
              <span className="text-white font-medium">{engineStatus?.active_jobs || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Total Jobs:</span>
              <span className="text-white font-medium">{engineStatus?.total_jobs || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Active Timers:</span>
              <span className="text-white font-medium">{engineStatus?.active_timers || 0}</span>
            </div>
          </div>

          {/* Available Emergency Endpoints */}
          <div className="mt-6 pt-4 border-t border-gray-700">
            <h4 className="text-white font-medium mb-2">Available Emergency Endpoints:</h4>
            <div className="space-y-1 text-sm">
              <div className="text-gray-400">
                <code className="text-yellow-400">POST</code> {engineStatus?.emergency_endpoints?.stop_all || '/api/crawl/emergency/stop-all'}
              </div>
              <div className="text-gray-400">
                <code className="text-red-400">POST</code> {engineStatus?.emergency_endpoints?.kill_engine || '/api/crawl/emergency/kill-engine'}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Usage Guidelines */}
      <div className="mt-6 bg-blue-900/20 border border-blue-600 rounded-lg p-4">
        <h3 className="text-blue-400 font-medium mb-2">📋 Usage Guidelines</h3>
        <div className="text-gray-300 text-sm space-y-1">
          <p><strong>Emergency Stop:</strong> Use when jobs are consuming too many resources or behaving unexpectedly</p>
          <p><strong>Nuclear Reset:</strong> Use only when emergency stop fails or the engine becomes unresponsive</p>
          <p><strong>Safety:</strong> Both operations require confirmation dialogs to prevent accidental execution</p>
        </div>
      </div>
    </div>
  )
}

export default System
