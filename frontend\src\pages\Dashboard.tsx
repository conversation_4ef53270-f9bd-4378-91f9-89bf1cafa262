import React, { useState } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { Button, ButtonGroup } from 'flowbite-react'
import { crawlApi, loggingApi } from '../lib/api'

const Dashboard: React.FC = () => {
  const [activeView, setActiveView] = useState<'overview' | 'jobs' | 'monitoring' | 'settings'>('overview')

  const { data: engineStatus, isLoading: statusLoading } = useQuery({
    queryKey: ['engine-status'],
    queryFn: crawlApi.getEngineStatus,
    refetchInterval: 5000,
  })

  const { data: jobs, isLoading: jobsLoading } = useQuery({
    queryKey: ['jobs'],
    queryFn: crawlApi.getJobs,
    refetchInterval: 2000,
  })

  const handleCreateDemo = async () => {
    try {
      const result = await crawlApi.createDemo()
      alert(`Created ${result.jobs_created} demo jobs!`)
    } catch (error) {
      alert('Failed to create demo jobs')
    }
  }

  const handleEmergencyStop = async () => {
    if (confirm('Are you sure you want to stop all jobs?')) {
      try {
        await crawlApi.emergencyStopAll()
        alert('All jobs stopped!')
      } catch (error) {
        alert('Failed to stop jobs')
      }
    }
  }

  // Logging preset mutation
  const setLoggingPresetMutation = useMutation({
    mutationFn: loggingApi.setPreset,
    onSuccess: (data) => {
      alert(data.message)
    },
    onError: () => {
      alert('Failed to set logging preset')
    },
  })

  const handleSetLoggingPreset = (preset: string) => {
    setLoggingPresetMutation.mutate(preset)
  }

  if (statusLoading || jobsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  const runningJobs = jobs?.filter(job => job.status === 'running') || []
  const completedJobs = jobs?.filter(job => job.status === 'completed') || []
  const failedJobs = jobs?.filter(job => job.status === 'failed') || []

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
        <p className="text-gray-400">SimWeaver Web Crawler Management</p>
      </div>

      {/* Navigation ButtonGroup */}
      <div className="mb-8">
        <ButtonGroup>
          <Button
            color={activeView === 'overview' ? 'primary' : 'secondary'}
            onClick={() => setActiveView('overview')}
            className="text-white"
          >
            <span className="mr-2">📊</span>
            Overview
          </Button>
          <Button
            color={activeView === 'jobs' ? 'primary' : 'secondary'}
            onClick={() => setActiveView('jobs')}
            className="text-white"
          >
            <span className="mr-2">⚙️</span>
            Job Management
          </Button>
          <Button
            color={activeView === 'monitoring' ? 'primary' : 'secondary'}
            onClick={() => setActiveView('monitoring')}
            className="text-white"
          >
            <span className="mr-2">📈</span>
            Monitoring
          </Button>
          <Button
            color={activeView === 'settings' ? 'primary' : 'secondary'}
            onClick={() => setActiveView('settings')}
            className="text-white"
          >
            <span className="mr-2">⚙️</span>
            Settings
          </Button>
        </ButtonGroup>
      </div>

      {/* Content based on active view */}
      {activeView === 'overview' && (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold">🚀</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-400">Engine Status</p>
                  <p className="text-2xl font-bold text-white">{engineStatus?.engine_healthy ? 'Healthy' : 'Unhealthy'}</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold">▶️</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-400">Running Jobs</p>
                  <p className="text-2xl font-bold text-white">{runningJobs.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold">✅</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-400">Completed</p>
                  <p className="text-2xl font-bold text-white">{completedJobs.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold">❌</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-400">Failed</p>
                  <p className="text-2xl font-bold text-white">{failedJobs.length}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Jobs */}
          <div>
            <h2 className="text-xl font-semibold text-white mb-4">Recent Jobs</h2>
            <div className="bg-gray-800 rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-700">
                <h3 className="text-lg font-medium text-white">Latest Activity</h3>
              </div>
              <div className="divide-y divide-gray-700">
                {jobs?.slice(0, 5).map((job) => (
                  <div key={job.job_id} className="px-6 py-4 flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-3 ${
                        job.status === 'running' ? 'bg-green-500' :
                        job.status === 'completed' ? 'bg-blue-500' :
                        job.status === 'failed' ? 'bg-red-500' :
                        'bg-yellow-500'
                      }`}></div>
                      <div>
                        <p className="text-white font-medium">{job.start_url}</p>
                        <p className="text-gray-400 text-sm">
                          {job.stats.pages_crawled}/{job.stats.total_pages} pages • {job.stats.total_pages > 0 ? ((job.stats.pages_crawled / job.stats.total_pages) * 100).toFixed(1) : 0}% complete
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-white font-medium capitalize">{job.status}</p>
                      <p className="text-gray-400 text-sm">{job.stats.pages_per_second.toFixed(2)} p/s</p>
                    </div>
                  </div>
                )) || (
                  <div className="px-6 py-8 text-center text-gray-400">
                    No jobs found. Create some demo jobs to get started!
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}

      {activeView === 'jobs' && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Job Management</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-medium text-white mb-3">Quick Actions</h3>
              <div className="flex flex-wrap gap-3">
                <Button
                  onClick={handleCreateDemo}
                  color="blue"
                  className="flex items-center"
                >
                  <span className="mr-2">🚀</span>
                  Create Demo Jobs
                </Button>
                <Button
                  onClick={handleEmergencyStop}
                  color="failure"
                  className="flex items-center text-red-500"
                >
                  <span className="mr-2">🛑</span>
                  Emergency Stop All
                </Button>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-medium text-white mb-3">Job Statistics</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Jobs:</span>
                  <span className="text-white font-medium">{jobs?.length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Running:</span>
                  <span className="text-green-400 font-medium">{runningJobs.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Completed:</span>
                  <span className="text-blue-400 font-medium">{completedJobs.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Failed:</span>
                  <span className="text-red-400 font-medium">{failedJobs.length}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeView === 'monitoring' && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">System Monitoring</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-medium text-white mb-3">Engine Health</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Status:</span>
                  <span className={`px-2 py-1 rounded text-sm font-medium ${
                    engineStatus?.engine_healthy
                      ? 'bg-green-900 text-green-200'
                      : 'bg-red-900 text-red-200'
                  }`}>
                    {engineStatus?.engine_healthy ? 'Healthy' : 'Unhealthy'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Active Jobs:</span>
                  <span className="text-white font-medium">{engineStatus?.active_jobs || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Total Jobs:</span>
                  <span className="text-white font-medium">{engineStatus?.total_jobs || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Active Timers:</span>
                  <span className="text-white font-medium">{engineStatus?.active_timers || 0}</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-medium text-white mb-3">Warnings</h3>
              <div className="space-y-2">
                {engineStatus?.warnings && engineStatus.warnings.length > 0 ? (
                  engineStatus.warnings.map((warning, index) => (
                    <div key={index} className="flex items-center p-2 bg-yellow-900 rounded text-yellow-200 text-sm">
                      <span className="mr-2">⚠️</span>
                      {warning}
                    </div>
                  ))
                ) : (
                  <div className="text-gray-400 text-sm">No warnings</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeView === 'settings' && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Settings</h2>
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-medium text-white mb-3">Logging Controls</h3>
            <p className="text-gray-400 mb-4">Configure logging levels for different system components</p>
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={() => handleSetLoggingPreset('minimal')}
                disabled={setLoggingPresetMutation.isPending}
                color="gray"
                size="sm"
              >
                Minimal
              </Button>
              <Button
                onClick={() => handleSetLoggingPreset('debug')}
                disabled={setLoggingPresetMutation.isPending}
                color="purple"
                size="sm"
              >
                Debug
              </Button>
              <Button
                onClick={() => handleSetLoggingPreset('performance')}
                disabled={setLoggingPresetMutation.isPending}
                color="blue"
                size="sm"
              >
                Performance
              </Button>
              <Button
                onClick={() => handleSetLoggingPreset('full_trace')}
                disabled={setLoggingPresetMutation.isPending}
                color="yellow"
                size="sm"
              >
                Full Trace
              </Button>
            </div>
          </div>
        </div>
      )}


    </div>
  )
}

export default Dashboard
