import React, { createContext, useContext, useState, ReactNode } from 'react'

interface ToastMessage {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  message: string
  duration?: number
}

interface ToastContextType {
  showToast: (type: ToastMessage['type'], message: string, duration?: number) => void
  showSuccess: (message: string, duration?: number) => void
  showError: (message: string, duration?: number) => void
  showWarning: (message: string, duration?: number) => void
  showInfo: (message: string, duration?: number) => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export const useToast = () => {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

interface ToastProviderProps {
  children: ReactNode
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastMessage[]>([])

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  const showToast = (type: ToastMessage['type'], message: string, duration = 4000) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast: ToastMessage = { id, type, message, duration }
    
    setToasts(prev => [...prev, newToast])
    
    // Auto-remove toast after duration
    if (duration > 0) {
      setTimeout(() => removeToast(id), duration)
    }
  }

  const showSuccess = (message: string, duration?: number) => showToast('success', message, duration)
  const showError = (message: string, duration?: number) => showToast('error', message, duration)
  const showWarning = (message: string, duration?: number) => showToast('warning', message, duration)
  const showInfo = (message: string, duration?: number) => showToast('info', message, duration)

  const getToastIcon = (type: ToastMessage['type']) => {
    switch (type) {
      case 'success': return '✅'
      case 'error': return '❌'
      case 'warning': return '⚠️'
      case 'info': return 'ℹ️'
    }
  }

  const getToastStyles = (type: ToastMessage['type']) => {
    switch (type) {
      case 'success': return 'bg-green-800 border-green-600 text-green-200'
      case 'error': return 'bg-red-800 border-red-600 text-red-200'
      case 'warning': return 'bg-yellow-800 border-yellow-600 text-yellow-200'
      case 'info': return 'bg-blue-800 border-blue-600 text-blue-200'
    }
  }

  return (
    <ToastContext.Provider value={{ showToast, showSuccess, showError, showWarning, showInfo }}>
      {children}
      
      {/* Toast Container */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {toasts.map((toast) => (
          <div
            key={toast.id}
            className={`max-w-xs p-4 rounded-lg border shadow-lg ${getToastStyles(toast.type)}`}
          >
            <div className="flex items-center">
              <span className="text-lg mr-3">{getToastIcon(toast.type)}</span>
              <div className="flex-1 text-sm font-medium">
                {toast.message}
              </div>
              <button
                onClick={() => removeToast(toast.id)}
                className="ml-3 text-lg hover:opacity-70"
              >
                ×
              </button>
            </div>
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  )
}
