import React, { createContext, useContext, useState, ReactNode } from 'react'
import { Toast } from 'flowbite-react'
import { Hi<PERSON>heck, HiX, HiExclamation, HiInformationCircle } from 'react-icons/hi'

interface ToastMessage {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  message: string
  duration?: number
}

interface ToastContextType {
  showToast: (type: ToastMessage['type'], message: string, duration?: number) => void
  showSuccess: (message: string, duration?: number) => void
  showError: (message: string, duration?: number) => void
  showWarning: (message: string, duration?: number) => void
  showInfo: (message: string, duration?: number) => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export const useToast = () => {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

interface ToastProviderProps {
  children: ReactNode
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastMessage[]>([])

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  const showToast = (type: ToastMessage['type'], message: string, duration = 4000) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast: ToastMessage = { id, type, message, duration }
    
    setToasts(prev => [...prev, newToast])
    
    // Auto-remove toast after duration
    if (duration > 0) {
      setTimeout(() => removeToast(id), duration)
    }
  }

  const showSuccess = (message: string, duration?: number) => showToast('success', message, duration)
  const showError = (message: string, duration?: number) => showToast('error', message, duration)
  const showWarning = (message: string, duration?: number) => showToast('warning', message, duration)
  const showInfo = (message: string, duration?: number) => showToast('info', message, duration)

  const getToastIcon = (type: ToastMessage['type']) => {
    switch (type) {
      case 'success': return <HiCheck className="h-5 w-5" />
      case 'error': return <HiX className="h-5 w-5" />
      case 'warning': return <HiExclamation className="h-5 w-5" />
      case 'info': return <HiInformationCircle className="h-5 w-5" />
    }
  }

  const getToastColor = (type: ToastMessage['type']) => {
    switch (type) {
      case 'success': return 'bg-green-100 text-green-500 dark:bg-green-800 dark:text-green-200'
      case 'error': return 'bg-red-100 text-red-500 dark:bg-red-800 dark:text-red-200'
      case 'warning': return 'bg-yellow-100 text-yellow-500 dark:bg-yellow-800 dark:text-yellow-200'
      case 'info': return 'bg-blue-100 text-blue-500 dark:bg-blue-800 dark:text-blue-200'
    }
  }

  return (
    <ToastContext.Provider value={{ showToast, showSuccess, showError, showWarning, showInfo }}>
      {children}
      
      {/* Toast Container */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {toasts.map((toast) => (
          <Toast key={toast.id} className="max-w-xs">
            <div className={`inline-flex h-8 w-8 shrink-0 items-center justify-center rounded-lg ${getToastColor(toast.type)}`}>
              {getToastIcon(toast.type)}
            </div>
            <div className="ml-3 text-sm font-normal text-gray-900 dark:text-white">
              {toast.message}
            </div>
            <Toast.Toggle 
              onDismiss={() => removeToast(toast.id)}
              className="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
            />
          </Toast>
        ))}
      </div>
    </ToastContext.Provider>
  )
}
