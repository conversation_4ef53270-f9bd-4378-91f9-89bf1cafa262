import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { crawlApi } from './lib/api'
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import Jobs from './pages/Jobs'
import Logs from './pages/Logs'
import System from './pages/System'
import './App.css'

function App() {
  // Test API connectivity on app load
  const { data: engineStatus, isLoading, error } = useQuery({
    queryKey: ['engine-status'],
    queryFn: crawlApi.getEngineStatus,
    refetchInterval: 5000, // Refresh every 5 seconds
  })

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Connecting to SimWeaver...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold mb-2">Connection Failed</h1>
          <p className="text-gray-400 mb-4">
            Cannot connect to SimWeaver backend at http://localhost:8000
          </p>
          <p className="text-sm text-gray-500">
            Make sure the backend is running: <code>uv run sig.py start</code>
          </p>
        </div>
      </div>
    )
  }

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/jobs" element={<Jobs />} />
        <Route path="/logs" element={<Logs />} />
        <Route path="/system" element={<System />} />
      </Routes>
    </Layout>
  )
}

export default App
