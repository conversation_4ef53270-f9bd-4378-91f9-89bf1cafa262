@echo off
echo Starting SpigaUI Development Environment...
echo.

REM Check if uv is installed
where uv >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: uv is not installed or not in PATH
    echo Please install uv first: https://docs.astral.sh/uv/getting-started/installation/
    pause
    exit /b 1
)

REM Check if npm is installed
where npm >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: npm is not installed or not in PATH
    echo Please install Node.js first: https://nodejs.org/
    pause
    exit /b 1
)

echo Installing/syncing backend dependencies...
uv sync
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to sync backend dependencies
    pause
    exit /b 1
)

echo Installing frontend dependencies...
cd frontend
if not exist node_modules (
    npm install
    if %ERRORLEVEL% neq 0 (
        echo Error: Failed to install frontend dependencies
        pause
        exit /b 1
    )
)
cd ..

echo.
echo Starting servers...
echo Backend will be available at: http://127.0.0.1:8000
echo Frontend will be available at: http://localhost:3000
echo API Documentation: http://127.0.0.1:8000/api/docs
echo.
echo Press Ctrl+C to stop both servers
echo.

REM Start backend in background
start "SpigaUI Backend" cmd /c "uv run python -m spigaui.main"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend in background
start "SpigaUI Frontend" cmd /c "cd frontend && npm run dev"

echo Both servers are starting...
echo Check the opened terminal windows for server status.
echo.
pause
