
# Mermaid Stylesheet Guidelines

This document provides standardized styling guidelines for creating consistent Mermaid diagrams across project documentation. Mermaid diagrams are rapidly evolving in terms of their API and features, and it's important to check for the latest documentation at [https://mermaid.js.org](https://mermaid.js.org) to resolve any errors.

Make sure each diagram has figure number and name in the format: **Figure X: Descriptive Name**

## Color Guidelines

### Base Colors

Use these base colors for primary elements in diagrams:

| Purpose | Color (Hex) | Example Use |
|---------|-------------|-------------|
| Primary | `#3498DB` | Main process nodes |
| Secondary | `#2ECC71` | Supporting process nodes |
| Highlight | `#F39C12` | Decision nodes, important elements |
| Warning | `#E74C3C` | Error states, warnings |
| Neutral | `#95A5A6` | Background elements, containers |

### Line Colors for Dark Backgrounds

When creating diagrams for dark backgrounds, use these high-contrast line colors:

| Line Type | Color (Hex) | Example Use |
|-----------|-------------|-------------|
| Standard | `#00BFFF` | Default connections (Bright blue) |
| Success | `#39FF14` | Positive flow paths (Neon green) |
| Warning | `#FFA500` | Conditional paths (Orange) |
| Error | `#FF3131` | Error paths (Bright red) |
| Highlight | `#FF00FF` | Important connections (Magenta) |
| Special | `#7FFFD4` | Special connections (Aquamarine) |
| Dotted | `#FFFFFF` | Weak or optional connections (White) |
| Thick | `#FFD700` | Emphasized connections (Gold) |
| Invisible | `transparent` | Layout connections (Transparent) |
| Bidirectional | `#9370DB` | Two-way relationships (Medium Purple) |

These colors are specifically chosen to maintain high visibility against dark backgrounds. For light backgrounds, you may need to use darker variants of these colors.

### Dark Mode Considerations

When creating diagrams for dark-themed environments:

1. **Link visibility**: Ensure link colors are bright enough to stand out against dark backgrounds
2. **Text in links**: For link labels, use high-contrast text colors (black or very dark colors)
3. **Background compatibility**: Test your diagrams against the actual background color of your documentation
4. **Node borders**: Consider using lighter border colors (`stroke:#FFF` or `stroke:#CCC`) for nodes to improve definition

For environments that switch between light and dark modes, consider providing two versions of critical diagrams or use colors that work reasonably well in both modes.

### Text Contrast Guidelines

For optimal readability, follow these contrast guidelines:

1. **Dark backgrounds** (Primary, Warning): Use white text (`color:#FFFFFF`)
2. **Light backgrounds** (Secondary, Highlight): Use black text (`color:#000000`)
3. **Neutral backgrounds** (like `#95A5A6`): Use darker text (`color:#000000`) instead of dark gray (`#333333`) for better contrast

When using the Neutral color (`#95A5A6`) as background:
- Use `color:#000000` for maximum readability
- Alternatively, if a softer look is needed, use no darker than `color:#222222`
- Avoid using `color:#333333` with light backgrounds as it fails accessibility contrast requirements

## Compatibility Guidelines

To ensure diagrams render correctly across different environments and Mermaid versions, follow these best practices:

### Basic Syntax

1. **Use simple diagram type declarations**:
   ```
   graph TD    # Top-down flowchart
   graph LR    # Left-right flowchart
   pie         # Pie chart
   gantt       # Gantt chart
   sequenceDiagram  # Sequence diagram
   classDiagram     # Class diagram
   ```

2. **Avoid initialization directives**: 
   - Do NOT use `%%{init: {...}}%%` directives unless absolutely necessary
   - These can cause compatibility issues across different Mermaid renderers

3. **Use frontmatter sparingly**:
   - If configuration is needed, prefer YAML frontmatter format:
   ```
   ---
   config:
     theme: default
     logLevel: error
   ---
   ```

4. **Prefer standard styling over custom configuration**:
   ```
   style A fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
   linkStyle 0 stroke:#00BFFF,stroke-width:2px
   ```

### Version Compatibility

1. **Stick to core features**: Use only widely supported features available in most Mermaid versions
2. **Test in multiple environments**: Verify diagrams render correctly in:
   - GitHub Markdown
   - VS Code with Markdown Preview
   - Documentation sites
   - Local Markdown viewers

3. **Avoid beta features**: Features marked with `-beta` suffix may change or be unsupported

### Troubleshooting

If you encounter "No diagram type detected" errors:

1. Check the diagram type declaration (e.g., `graph TD`, `pie`, etc.)
2. Remove any initialization directives (`%%{init: {...}}%%`)
3. Simplify the diagram to basic elements, then add complexity incrementally
4. Verify syntax with the [Mermaid Live Editor](https://mermaid.live/)

### Example Style Definitions

```mermaid
graph LR
    A[Primary] --> B{Decision}
    B -->|Yes| C[Success]
    B -->|No| D[Error]
    
    style A fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style B fill:#F39C12,stroke:#333,stroke-width:2px,color:#000000
    style C fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style D fill:#E74C3C,stroke:#333,stroke-width:2px,color:#FFFFFF
    
    linkStyle 0 stroke:#00BFFF,stroke-width:2px
    linkStyle 1 stroke:#39FF14,stroke-width:2px
    linkStyle 2 stroke:#FF3131,stroke-width:2px
```

## Node Shape Guidelines

Choose appropriate shapes based on node function:

| Function | Shape | Example |
|----------|-------|---------|
| Start/End | Rounded rectangle `[Text]` | `A[Start]` |
| Process | Rectangle `[Text]` | `B[Process data]` |
| Decision | Diamond `{Text}` | `C{Is valid?}` |
| Input/Output | Parallelogram `[/Text/]` | `D[/Display result/]` |
| Database | Cylinder `[(Text)]` | `E[(Store data)]` |

## Line Style Guidelines

Use consistent line styles to indicate relationship types:

| Relationship | Style | Example |
|--------------|-------|---------|
| Standard flow | Solid arrow `-->` | `A --> B` |
| Conditional flow | Labeled arrow `-->|Label|` | `A -->|Yes| B` |
| Asynchronous flow | Dotted arrow `-.->` | `A -.-> B` |
| Thick/Important flow | Thick arrow `==>` | `A ==> B` |

### Styling Line Connections

Use the `linkStyle` directive to customize the appearance of connections:

```
linkStyle 0 stroke:#00BFFF,stroke-width:2px
linkStyle 1,2 stroke:#FF3131,stroke-width:2px,stroke-dasharray: 5 5
```

- The number refers to the link index (starting from 0)
- Multiple links can be styled at once by separating indices with commas
- Common properties: `stroke`, `stroke-width`, `stroke-dasharray`

## Accessibility Considerations

1. Don't rely solely on color to convey information
2. Use labels and shapes to reinforce meaning
3. Maintain high contrast between text and background
4. Keep diagrams simple and focused on key information

## Example Complete Diagram

```mermaid
graph TD
    A[Start] --> B{Input Valid?}
    B -->|Yes| C[Process Data]
    B -->|No| D[Show Error]
    C --> E[(Save to Database)]
    D --> A
    E --> F[Complete]
    
    style A fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style B fill:#F39C12,stroke:#333,stroke-width:2px,color:#000000
    style C fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style D fill:#E74C3C,stroke:#333,stroke-width:2px,color:#FFFFFF
    style E fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style F fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    
    linkStyle 0 stroke:#00BFFF,stroke-width:2px
    linkStyle 1 stroke:#39FF14,stroke-width:2px
    linkStyle 2 stroke:#FF3131,stroke-width:2px
    linkStyle 3,5 stroke:#00BFFF,stroke-width:2px
    linkStyle 4 stroke:#FFA500,stroke-width:2px,stroke-dasharray: 5 5
```

